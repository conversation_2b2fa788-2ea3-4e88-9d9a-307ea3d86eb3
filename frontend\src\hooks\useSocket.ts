"use client"

import { useEffect, useRef, useState } from 'react'
import { io, Socket } from 'socket.io-client'

interface CameraEvent {
  cameraId: string
  event: {
    id: string
    type: string
    title: string
    description: string
    severity: string
    timestamp: string
    metadata?: any
  }
}

interface CameraStatusUpdate {
  cameraId: string
  status: 'online' | 'offline' | 'error'
}

interface CameraArmedUpdate {
  cameraId: string
}

interface SocketEvents {
  cameraEvent: (data: CameraEvent) => void
  cameraStatusUpdate: (data: CameraStatusUpdate) => void
  cameraArmed: (data: CameraArmedUpdate) => void
  cameraDisarmed: (data: CameraArmedUpdate) => void
  welcome: (data: { message: string; timestamp: string }) => void
}

export function useSocket() {
  const socketRef = useRef<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [connectionError, setConnectionError] = useState<string | null>(null)

  useEffect(() => {
    // Initialize socket connection
    const socket = io(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000', {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    })

    socketRef.current = socket

    // Connection event handlers
    socket.on('connect', () => {
      console.log('Socket.IO connected:', socket.id)
      setIsConnected(true)
      setConnectionError(null)
    })

    socket.on('disconnect', (reason) => {
      console.log('Socket.IO disconnected:', reason)
      setIsConnected(false)
    })

    socket.on('connect_error', (error) => {
      console.error('Socket.IO connection error:', error)
      setConnectionError(error.message)
      setIsConnected(false)
    })

    socket.on('welcome', (data) => {
      console.log('Welcome message:', data)
    })

    // Cleanup on unmount
    return () => {
      socket.disconnect()
    }
  }, [])

  // Subscribe to specific events
  const subscribe = <K extends keyof SocketEvents>(
    event: K,
    callback: SocketEvents[K]
  ) => {
    if (socketRef.current) {
      socketRef.current.on(event, callback)
    }

    // Return unsubscribe function
    return () => {
      if (socketRef.current) {
        socketRef.current.off(event, callback)
      }
    }
  }

  // Emit events
  const emit = (event: string, data?: any) => {
    if (socketRef.current && isConnected) {
      socketRef.current.emit(event, data)
    }
  }

  return {
    socket: socketRef.current,
    isConnected,
    connectionError,
    subscribe,
    emit
  }
}

// Hook for camera-specific events
export function useCameraEvents() {
  const { subscribe, isConnected } = useSocket()
  const [cameraEvents, setCameraEvents] = useState<CameraEvent[]>([])
  const [cameraStatuses, setCameraStatuses] = useState<Map<string, string>>(new Map())

  useEffect(() => {
    // Subscribe to camera events
    const unsubscribeEvent = subscribe('cameraEvent', (data) => {
      console.log('Camera event received:', data)
      setCameraEvents(prev => [data, ...prev.slice(0, 99)]) // Keep last 100 events
    })

    const unsubscribeStatus = subscribe('cameraStatusUpdate', (data) => {
      console.log('Camera status update:', data)
      setCameraStatuses(prev => new Map(prev.set(data.cameraId, data.status)))
    })

    const unsubscribeArmed = subscribe('cameraArmed', (data) => {
      console.log('Camera armed:', data)
      // You can add additional logic here for armed state updates
    })

    const unsubscribeDisarmed = subscribe('cameraDisarmed', (data) => {
      console.log('Camera disarmed:', data)
      // You can add additional logic here for disarmed state updates
    })

    return () => {
      unsubscribeEvent()
      unsubscribeStatus()
      unsubscribeArmed()
      unsubscribeDisarmed()
    }
  }, [subscribe])

  return {
    isConnected,
    cameraEvents,
    cameraStatuses,
    clearEvents: () => setCameraEvents([])
  }
}
