"use client"

import React, { useState, useEffect } from "react"
import { motion } from "framer-motion"
import {
  Camera,
  Settings,
  Power,
  PowerOff,
  Eye,
  EyeOff,
  Wifi,
  WifiOff,
  MoreVertical,
  Plus,
  Search,
  Filter,
  Loader2,
  AlertCircle,
  Download,
  Play,
  Activity,
  Shield,
  ShieldOff
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { apiService } from "@/lib/api"
import { Camera as CameraType } from "@/types/api"
import { ProtectedRoute } from "@/components/ProtectedRoute"
import { useCameraEvents } from "@/hooks/useSocket"

// Helper function to format last seen time
const formatLastSeen = (lastHeartbeat?: string): string => {
  if (!lastHeartbeat) return "Never"

  const now = new Date()
  const lastSeen = new Date(lastHeartbeat)
  const diffMs = now.getTime() - lastSeen.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)

  if (diffMinutes < 1) return "Just now"
  if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
  return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
}

// Helper function to get status color
const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'online':
      return 'bg-green-500'
    case 'offline':
      return 'bg-red-500'
    case 'error':
      return 'bg-yellow-500'
    default:
      return 'bg-gray-500'
  }
}

// Helper function to get status badge variant
const getStatusBadgeVariant = (status: string) => {
  switch (status.toLowerCase()) {
    case 'online':
      return 'default'
    case 'offline':
      return 'destructive'
    case 'error':
      return 'secondary'
    default:
      return 'outline'
  }
}

function CamerasPage() {
  // State management
  const [cameras, setCameras] = useState<CameraType[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [armingCamera, setArmingCamera] = useState<string | null>(null)
  
  // Real-time updates
  const { isConnected, cameraStatuses } = useCameraEvents()

  // Load cameras on component mount
  useEffect(() => {
    loadCameras()
  }, [])

  const loadCameras = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiService.getCameras()
      setCameras(response.data)
    } catch (err) {
      console.error('Failed to load cameras:', err)
      setError('Failed to load cameras. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // ARM camera function
  const handleArmCamera = async (cameraId: string) => {
    try {
      setArmingCamera(cameraId)
      await apiService.armCamera(cameraId)
      
      // Update local state
      setCameras(prev => prev.map(camera => 
        camera._id === cameraId 
          ? { ...camera, isArmed: true }
          : camera
      ))
    } catch (err) {
      console.error('Failed to arm camera:', err)
      alert('Failed to arm camera. Please try again.')
    } finally {
      setArmingCamera(null)
    }
  }

  // DISARM camera function
  const handleDisarmCamera = async (cameraId: string) => {
    try {
      setArmingCamera(cameraId)
      await apiService.disarmCamera(cameraId)
      
      // Update local state
      setCameras(prev => prev.map(camera => 
        camera._id === cameraId 
          ? { ...camera, isArmed: false }
          : camera
      ))
    } catch (err) {
      console.error('Failed to disarm camera:', err)
      alert('Failed to disarm camera. Please try again.')
    } finally {
      setArmingCamera(null)
    }
  }

  // Filter cameras based on search and status
  const filteredCameras = (cameras || []).filter(camera => {
    const matchesSearch = camera.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         camera.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         camera.ipAddress.includes(searchTerm)

    const matchesStatus = statusFilter === "all" || camera.status === statusFilter

    return matchesSearch && matchesStatus
  })

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading cameras...</span>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Failed to Load Cameras</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={loadCameras}>
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Camera Management
          </h1>
          <div className="flex items-center space-x-4 mt-2">
            <p className="text-gray-600 dark:text-gray-400">
              Monitor and manage all your HikVision cameras
            </p>
            <div className={`flex items-center space-x-1 text-sm ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span>{isConnected ? 'Live' : 'Offline'}</span>
            </div>
          </div>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          Add Camera
        </Button>
      </div>

      <div className="flex items-center space-x-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search cameras..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">All Status</option>
          <option value="online">Online</option>
          <option value="offline">Offline</option>
          <option value="error">Error</option>
        </select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCameras.map((camera) => (
          <motion.div
            key={camera._id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Camera className="h-5 w-5 text-blue-600" />
                    <CardTitle className="text-lg">{camera.name}</CardTitle>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${getStatusColor(camera.status)}`} />
                    <Badge variant={getStatusBadgeVariant(camera.status)}>
                      {camera.status}
                    </Badge>
                  </div>
                </div>
                <CardDescription className="flex items-center space-x-4">
                  <span>{camera.location}</span>
                  <span className="text-xs">•</span>
                  <span>{camera.ipAddress}</span>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Last Seen:</span>
                    <span>{formatLastSeen(camera.lastHeartbeat)}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Armed Status:</span>
                    <div className="flex items-center space-x-1">
                      {camera.isArmed ? (
                        <Shield className="h-4 w-4 text-green-600" />
                      ) : (
                        <ShieldOff className="h-4 w-4 text-gray-400" />
                      )}
                      <span className={camera.isArmed ? 'text-green-600' : 'text-gray-400'}>
                        {camera.isArmed ? 'Armed' : 'Disarmed'}
                      </span>
                    </div>
                  </div>

                  <div className="flex space-x-2 pt-2">
                    {camera.isArmed ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDisarmCamera(camera._id)}
                        disabled={armingCamera === camera._id}
                        className="flex-1"
                      >
                        {armingCamera === camera._id ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <ShieldOff className="h-4 w-4 mr-2" />
                        )}
                        Disarm
                      </Button>
                    ) : (
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => handleArmCamera(camera._id)}
                        disabled={armingCamera === camera._id}
                        className="flex-1 bg-green-600 hover:bg-green-700"
                      >
                        {armingCamera === camera._id ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <Shield className="h-4 w-4 mr-2" />
                        )}
                        Arm
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {filteredCameras.length === 0 && (
        <div className="text-center py-12">
          <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Cameras Found</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || statusFilter !== "all" 
              ? "No cameras match your current filters." 
              : "Get started by adding your first camera."}
          </p>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Add Camera
          </Button>
        </div>
      )}
    </div>
  )
}

export default function CamerasPageWrapper() {
  return (
    <ProtectedRoute requiredRole="operator">
      <CamerasPage />
    </ProtectedRoute>
  )
}
