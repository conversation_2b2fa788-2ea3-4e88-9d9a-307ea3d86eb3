"use client"

import React, { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Bell, 
  Camera, 
  Shield, 
  ShieldOff, 
  AlertTriangle, 
  CheckCircle, 
  X,
  Wifi,
  WifiOff
} from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useCameraEvents } from '@/hooks/useSocket'

interface Notification {
  id: string
  type: 'event' | 'status' | 'armed' | 'disarmed'
  title: string
  message: string
  severity: 'info' | 'warning' | 'error' | 'success'
  timestamp: Date
  cameraId?: string
  cameraName?: string
}

export function RealTimeNotifications() {
  const { isConnected, cameraEvents, cameraStatuses } = useCameraEvents()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isVisible, setIsVisible] = useState(true)

  // Convert camera events to notifications
  useEffect(() => {
    cameraEvents.forEach(eventData => {
      const notification: Notification = {
        id: `event-${eventData.event.id}-${Date.now()}`,
        type: 'event',
        title: eventData.event.title || 'Camera Event',
        message: eventData.event.description || `${eventData.event.type} detected`,
        severity: eventData.event.severity as any || 'info',
        timestamp: new Date(eventData.event.timestamp),
        cameraId: eventData.cameraId,
        cameraName: `Camera ${eventData.cameraId.slice(-4)}`
      }

      setNotifications(prev => [notification, ...prev.slice(0, 9)]) // Keep last 10 notifications
    })
  }, [cameraEvents])

  // Convert camera status updates to notifications
  useEffect(() => {
    cameraStatuses.forEach((status, cameraId) => {
      const notification: Notification = {
        id: `status-${cameraId}-${Date.now()}`,
        type: 'status',
        title: 'Camera Status Update',
        message: `Camera ${cameraId.slice(-4)} is now ${status}`,
        severity: status === 'online' ? 'success' : status === 'offline' ? 'warning' : 'error',
        timestamp: new Date(),
        cameraId,
        cameraName: `Camera ${cameraId.slice(-4)}`
      }

      setNotifications(prev => [notification, ...prev.slice(0, 9)])
    })
  }, [cameraStatuses])

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const clearAllNotifications = () => {
    setNotifications([])
  }

  const getNotificationIcon = (type: string, severity: string) => {
    switch (type) {
      case 'event':
        return severity === 'error' ? <AlertTriangle className="h-4 w-4" /> : <Camera className="h-4 w-4" />
      case 'status':
        return severity === 'success' ? <Wifi className="h-4 w-4" /> : <WifiOff className="h-4 w-4" />
      case 'armed':
        return <Shield className="h-4 w-4" />
      case 'disarmed':
        return <ShieldOff className="h-4 w-4" />
      default:
        return <Bell className="h-4 w-4" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200'
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200'
    }
  }

  if (!isVisible || notifications.length === 0) {
    return (
      <div className="fixed top-4 right-4 z-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex items-center space-x-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-3"
        >
          <div className={`flex items-center space-x-2 ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
            {isConnected ? <Wifi className="h-4 w-4" /> : <WifiOff className="h-4 w-4" />}
            <span className="text-sm font-medium">
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          {notifications.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(true)}
              className="p-1"
            >
              <Bell className="h-4 w-4" />
              <Badge variant="destructive" className="ml-1 text-xs">
                {notifications.length}
              </Badge>
            </Button>
          )}
        </motion.div>
      </div>
    )
  }

  return (
    <div className="fixed top-4 right-4 z-50 w-80 max-h-96 overflow-hidden">
      <motion.div
        initial={{ opacity: 0, x: 300 }}
        animate={{ opacity: 1, x: 0 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl border"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2">
            <Bell className="h-5 w-5" />
            <h3 className="font-semibold">Live Updates</h3>
            <div className={`flex items-center space-x-1 ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
              {isConnected ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
              <span className="text-xs">
                {isConnected ? 'Live' : 'Offline'}
              </span>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            {notifications.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllNotifications}
                className="text-xs p-1"
              >
                Clear
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsVisible(false)}
              className="p-1"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Notifications */}
        <div className="max-h-80 overflow-y-auto">
          <AnimatePresence>
            {notifications.map((notification) => (
              <motion.div
                key={notification.id}
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className={`p-3 border-b border-l-4 ${getSeverityColor(notification.severity)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-2 flex-1">
                    {getNotificationIcon(notification.type, notification.severity)}
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm truncate">
                        {notification.title}
                      </p>
                      <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        {notification.message}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-gray-500">
                          {notification.timestamp.toLocaleTimeString()}
                        </span>
                        {notification.cameraName && (
                          <Badge variant="outline" className="text-xs">
                            {notification.cameraName}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeNotification(notification.id)}
                    className="p-1 ml-2"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
          
          {notifications.length === 0 && (
            <div className="p-6 text-center text-gray-500">
              <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No recent notifications</p>
              <p className="text-xs mt-1">
                {isConnected ? 'Listening for camera events...' : 'Waiting for connection...'}
              </p>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  )
}
