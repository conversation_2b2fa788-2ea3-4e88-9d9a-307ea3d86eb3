/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CAUGMENT%20NEW%20XML%20HIKVISION%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CAUGMENT%20NEW%20XML%20HIKVISION%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CAUGMENT%20NEW%20XML%20HIKVISION%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CAUGMENT%20NEW%20XML%20HIKVISION%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CAUGMENT%20NEW%20XML%20HIKVISION%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CAUGMENT%20NEW%20XML%20HIKVISION%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CLayoutWrapper.tsx%22%2C%22ids%22%3A%5B%22LayoutWrapper%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CLayoutWrapper.tsx%22%2C%22ids%22%3A%5B%22LayoutWrapper%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/LayoutWrapper.tsx */ \"(rsc)/./src/components/layout/LayoutWrapper.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CLayoutWrapper.tsx%22%2C%22ids%22%3A%5B%22LayoutWrapper%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNBVUdNRU5UJTIwTkVXJTIwWE1MJTIwSElLVklTSU9OJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWdHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxBVUdNRU5UIE5FVyBYTUwgSElLVklTSU9OXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxBVUdNRU5UIE5FVyBYTUwgSElLVklTSU9OXFxmcm9udGVuZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcQVVHTUVOVCBORVcgWE1MIEhJS1ZJU0lPTlxcZnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjcxOWNiMGZjM2Y2M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_layout_LayoutWrapper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/layout/LayoutWrapper */ \"(rsc)/./src/components/layout/LayoutWrapper.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"HikVision AI Monitoring System\",\n    description: \"Professional AI-enhanced security monitoring system for HikVision cameras\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased h-full bg-gray-50 dark:bg-gray-900`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_LayoutWrapper__WEBPACK_IMPORTED_MODULE_3__.LayoutWrapper, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\AUGMENT NEW XML HIKVISION\\frontend\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/layout/LayoutWrapper.tsx":
/*!*************************************************!*\
  !*** ./src/components/layout/LayoutWrapper.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LayoutWrapper: () => (/* binding */ LayoutWrapper)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const LayoutWrapper = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LayoutWrapper() from the server but LayoutWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\AUGMENT NEW XML HIKVISION\\frontend\\src\\components\\layout\\LayoutWrapper.tsx",
"LayoutWrapper",
);

/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\AUGMENT NEW XML HIKVISION\\frontend\\src\\contexts\\AuthContext.tsx",
"useAuth",
);const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\AUGMENT NEW XML HIKVISION\\frontend\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CLayoutWrapper.tsx%22%2C%22ids%22%3A%5B%22LayoutWrapper%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CLayoutWrapper.tsx%22%2C%22ids%22%3A%5B%22LayoutWrapper%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/LayoutWrapper.tsx */ \"(ssr)/./src/components/layout/LayoutWrapper.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CLayoutWrapper.tsx%22%2C%22ids%22%3A%5B%22LayoutWrapper%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNBVUdNRU5UJTIwTkVXJTIwWE1MJTIwSElLVklTSU9OJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQWdHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxBVUdNRU5UIE5FVyBYTUwgSElLVklTSU9OXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CAUGMENT%20NEW%20XML%20HIKVISION%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Camera,CheckCircle,Clock,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Camera,CheckCircle,Clock,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Camera,CheckCircle,Clock,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Camera,CheckCircle,Clock,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Camera,CheckCircle,Clock,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Camera,CheckCircle,Clock,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Camera,CheckCircle,Clock,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Camera,CheckCircle,Clock,Shield,TrendingUp,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(ssr)/./src/components/ProtectedRoute.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst stats = [\n    {\n        name: \"Active Cameras\",\n        value: \"24\",\n        change: \"+2\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"blue\"\n    },\n    {\n        name: \"Events Today\",\n        value: \"1,247\",\n        change: \"+12%\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: \"green\"\n    },\n    {\n        name: \"AI Detections\",\n        value: \"89\",\n        change: \"+5\",\n        changeType: \"positive\",\n        icon: _barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        color: \"purple\"\n    },\n    {\n        name: \"Security Alerts\",\n        value: \"3\",\n        change: \"-2\",\n        changeType: \"negative\",\n        icon: _barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        color: \"red\"\n    }\n];\nconst recentEvents = [\n    {\n        id: 1,\n        type: \"Person Detected\",\n        camera: \"Front Entrance\",\n        time: \"2 minutes ago\",\n        status: \"active\",\n        confidence: 94\n    },\n    {\n        id: 2,\n        type: \"Vehicle Recognition\",\n        camera: \"Parking Lot A\",\n        time: \"5 minutes ago\",\n        status: \"resolved\",\n        confidence: 87\n    },\n    {\n        id: 3,\n        type: \"Motion Alert\",\n        camera: \"Warehouse Door\",\n        time: \"12 minutes ago\",\n        status: \"investigating\",\n        confidence: 76\n    },\n    {\n        id: 4,\n        type: \"Face Recognition\",\n        camera: \"Main Lobby\",\n        time: \"18 minutes ago\",\n        status: \"resolved\",\n        confidence: 92\n    },\n    {\n        id: 5,\n        type: \"Intrusion Alert\",\n        camera: \"Perimeter Fence\",\n        time: \"25 minutes ago\",\n        status: \"active\",\n        confidence: 89\n    }\n];\nfunction Dashboard() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 space-y-6 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                children: \"Welcome back! Here's what's happening with your security system.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"View Reports\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Arm All Cameras\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-4\",\n                children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: index * 0.1\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"hover:shadow-lg transition-shadow duration-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                            children: stat.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                            className: `h-4 w-4 text-${stat.color}-600`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: `${stat.changeType === \"positive\" ? \"text-green-600\" : \"text-red-600\"}`,\n                                                    children: stat.change\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                \"from last hour\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this)\n                    }, stat.name, false, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 lg:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Recent Events\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Latest security events from your cameras\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: recentEvents.map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                            className: \"flex items-center justify-between p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\",\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: [\n                                                                event.status === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-red-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                event.status === \"resolved\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-green-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                event.status === \"investigating\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-amber-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                                    children: event.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 195,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                                    children: [\n                                                                        event.camera,\n                                                                        \" • \",\n                                                                        event.time\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        variant: event.status === \"active\" ? \"destructive\" : event.status === \"resolved\" ? \"success\" : \"warning\",\n                                                        size: \"sm\",\n                                                        children: [\n                                                            event.confidence,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, event.id, true, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Camera_CheckCircle_Clock_Shield_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"System Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Current status of all system components\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"Camera Network\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    variant: \"success\",\n                                                    children: \"Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"AI Processing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    variant: \"success\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"Database\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    variant: \"success\",\n                                                    children: \"Connected\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"Storage\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    variant: \"warning\",\n                                                    children: \"78% Full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                    variant: \"success\",\n                                                    children: \"Enabled\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\nfunction DashboardPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_5__.ProtectedRoute, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Dashboard, {}, void 0, false, {\n            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 277,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 276,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProtectedRoute.tsx":
/*!*******************************************!*\
  !*** ./src/components/ProtectedRoute.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProtectedRoute: () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ ProtectedRoute auto */ \n\n\n\n\nconst ProtectedRoute = ({ children, requiredRole = 'viewer' })=>{\n    const { user, loading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!loading && !isAuthenticated) {\n                router.push('/login');\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        loading,\n        isAuthenticated,\n        router\n    ]);\n    // Show loading spinner while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin text-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Redirect to login if not authenticated\n    if (!isAuthenticated) {\n        return null;\n    }\n    // Check role permissions\n    if (user && requiredRole) {\n        const roleHierarchy = {\n            viewer: 1,\n            operator: 2,\n            admin: 3\n        };\n        const userRoleLevel = roleHierarchy[user.role];\n        const requiredRoleLevel = roleHierarchy[requiredRole];\n        if (userRoleLevel < requiredRoleLevel) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                            children: \"Access Denied\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                            children: \"You don't have permission to access this page.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500 dark:text-gray-500\",\n                            children: [\n                                \"Required role: \",\n                                requiredRole,\n                                \" | Your role: \",\n                                user.role\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.tsx\",\n                lineNumber: 56,\n                columnNumber: 9\n            }, undefined);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/RealTimeNotifications.tsx":
/*!**************************************************!*\
  !*** ./src/components/RealTimeNotifications.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealTimeNotifications: () => (/* binding */ RealTimeNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Camera,Shield,ShieldOff,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Camera,Shield,ShieldOff,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Camera,Shield,ShieldOff,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Camera,Shield,ShieldOff,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Camera,Shield,ShieldOff,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Camera,Shield,ShieldOff,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Camera,Shield,ShieldOff,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Camera,Shield,ShieldOff,Wifi,WifiOff,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _hooks_useSocket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSocket */ \"(ssr)/./src/hooks/useSocket.ts\");\n/* __next_internal_client_entry_do_not_use__ RealTimeNotifications auto */ \n\n\n\n\n\n\nfunction RealTimeNotifications() {\n    const { isConnected, cameraEvents, cameraStatuses } = (0,_hooks_useSocket__WEBPACK_IMPORTED_MODULE_4__.useCameraEvents)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Convert camera events to notifications\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RealTimeNotifications.useEffect\": ()=>{\n            cameraEvents.forEach({\n                \"RealTimeNotifications.useEffect\": (eventData)=>{\n                    const notification = {\n                        id: `event-${eventData.event.id}-${Date.now()}`,\n                        type: 'event',\n                        title: eventData.event.title || 'Camera Event',\n                        message: eventData.event.description || `${eventData.event.type} detected`,\n                        severity: eventData.event.severity || 'info',\n                        timestamp: new Date(eventData.event.timestamp),\n                        cameraId: eventData.cameraId,\n                        cameraName: `Camera ${eventData.cameraId.slice(-4)}`\n                    };\n                    setNotifications({\n                        \"RealTimeNotifications.useEffect\": (prev)=>[\n                                notification,\n                                ...prev.slice(0, 9)\n                            ]\n                    }[\"RealTimeNotifications.useEffect\"]) // Keep last 10 notifications\n                    ;\n                }\n            }[\"RealTimeNotifications.useEffect\"]);\n        }\n    }[\"RealTimeNotifications.useEffect\"], [\n        cameraEvents\n    ]);\n    // Convert camera status updates to notifications\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RealTimeNotifications.useEffect\": ()=>{\n            cameraStatuses.forEach({\n                \"RealTimeNotifications.useEffect\": (status, cameraId)=>{\n                    const notification = {\n                        id: `status-${cameraId}-${Date.now()}`,\n                        type: 'status',\n                        title: 'Camera Status Update',\n                        message: `Camera ${cameraId.slice(-4)} is now ${status}`,\n                        severity: status === 'online' ? 'success' : status === 'offline' ? 'warning' : 'error',\n                        timestamp: new Date(),\n                        cameraId,\n                        cameraName: `Camera ${cameraId.slice(-4)}`\n                    };\n                    setNotifications({\n                        \"RealTimeNotifications.useEffect\": (prev)=>[\n                                notification,\n                                ...prev.slice(0, 9)\n                            ]\n                    }[\"RealTimeNotifications.useEffect\"]);\n                }\n            }[\"RealTimeNotifications.useEffect\"]);\n        }\n    }[\"RealTimeNotifications.useEffect\"], [\n        cameraStatuses\n    ]);\n    const removeNotification = (id)=>{\n        setNotifications((prev)=>prev.filter((n)=>n.id !== id));\n    };\n    const clearAllNotifications = ()=>{\n        setNotifications([]);\n    };\n    const getNotificationIcon = (type, severity)=>{\n        switch(type){\n            case 'event':\n                return severity === 'error' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 39\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 79\n                }, this);\n            case 'status':\n                return severity === 'success' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 41\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 72\n                }, this);\n            case 'armed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 16\n                }, this);\n            case 'disarmed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getSeverityColor = (severity)=>{\n        switch(severity){\n            case 'success':\n                return 'text-green-600 bg-green-50 border-green-200';\n            case 'warning':\n                return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n            case 'error':\n                return 'text-red-600 bg-red-50 border-red-200';\n            default:\n                return 'text-blue-600 bg-blue-50 border-blue-200';\n        }\n    };\n    if (!isVisible || notifications.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-4 right-4 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    scale: 0.8\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                className: \"flex items-center space-x-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex items-center space-x-2 ${isConnected ? 'text-green-600' : 'text-red-600'}`,\n                        children: [\n                            isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 28\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 59\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: isConnected ? 'Connected' : 'Disconnected'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: ()=>setIsVisible(true),\n                        className: \"p-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                variant: \"destructive\",\n                                className: \"ml-1 text-xs\",\n                                children: notifications.length\n                            }, void 0, false, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 w-80 max-h-96 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n            initial: {\n                opacity: 0,\n                x: 300\n            },\n            animate: {\n                opacity: 1,\n                x: 0\n            },\n            className: \"bg-white dark:bg-gray-800 rounded-lg shadow-xl border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4 border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold\",\n                                    children: \"Live Updates\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex items-center space-x-1 ${isConnected ? 'text-green-600' : 'text-red-600'}`,\n                                    children: [\n                                        isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 30\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 61\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs\",\n                                            children: isConnected ? 'Live' : 'Offline'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-1\",\n                            children: [\n                                notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: clearAllNotifications,\n                                    className: \"text-xs p-1\",\n                                    children: \"Clear\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: ()=>setIsVisible(false),\n                                    className: \"p-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-80 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                            children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        height: 'auto'\n                                    },\n                                    exit: {\n                                        opacity: 0,\n                                        height: 0\n                                    },\n                                    className: `p-3 border-b border-l-4 ${getSeverityColor(notification.severity)}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-2 flex-1\",\n                                                children: [\n                                                    getNotificationIcon(notification.type, notification.severity),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium text-sm truncate\",\n                                                                children: notification.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 dark:text-gray-400 mt-1\",\n                                                                children: notification.message\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500\",\n                                                                        children: notification.timestamp.toLocaleTimeString()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                                                        lineNumber: 203,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    notification.cameraName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs\",\n                                                                        children: notification.cameraName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                                                        lineNumber: 207,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>removeNotification(notification.id),\n                                                className: \"p-1 ml-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 17\n                                    }, this)\n                                }, notification.id, false, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        notifications.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Camera_Shield_ShieldOff_Wifi_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"No recent notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs mt-1\",\n                                    children: isConnected ? 'Listening for camera events...' : 'Waiting for connection...'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\RealTimeNotifications.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/RealTimeNotifications.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/LayoutWrapper.tsx":
/*!*************************************************!*\
  !*** ./src/components/layout/LayoutWrapper.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayoutWrapper: () => (/* binding */ LayoutWrapper)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./sidebar */ \"(ssr)/./src/components/layout/sidebar.tsx\");\n/* harmony import */ var _components_RealTimeNotifications__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/RealTimeNotifications */ \"(ssr)/./src/components/RealTimeNotifications.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ LayoutWrapper auto */ \n\n\n\n\n\n\nconst LayoutWrapper = ({ children })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isAuthenticated, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // Routes that don't need authentication (public routes)\n    const publicRoutes = [\n        '/login',\n        '/register'\n    ];\n    const isPublicRoute = publicRoutes.includes(pathname);\n    // Show loading spinner while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-6 w-6 animate-spin text-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\LayoutWrapper.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600 dark:text-gray-400\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\LayoutWrapper.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\LayoutWrapper.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\LayoutWrapper.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, undefined);\n    }\n    // For public routes (login/register), show full-width layout\n    if (isPublicRoute) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: children\n        }, void 0, false);\n    }\n    // For authenticated routes, show sidebar + main content\n    if (isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_sidebar__WEBPACK_IMPORTED_MODULE_4__.Sidebar, {}, void 0, false, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\LayoutWrapper.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-1 overflow-auto\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\LayoutWrapper.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RealTimeNotifications__WEBPACK_IMPORTED_MODULE_5__.RealTimeNotifications, {}, void 0, false, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\LayoutWrapper.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\LayoutWrapper.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, undefined);\n    }\n    // For protected routes when not authenticated, the ProtectedRoute component will handle redirect\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/LayoutWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Camera,LayoutDashboard,LogOut,Search,Settings,Shield,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Camera,LayoutDashboard,LogOut,Search,Settings,Shield,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Camera,LayoutDashboard,LogOut,Search,Settings,Shield,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Camera,LayoutDashboard,LogOut,Search,Settings,Shield,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Camera,LayoutDashboard,LogOut,Search,Settings,Shield,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Camera,LayoutDashboard,LogOut,Search,Settings,Shield,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Camera,LayoutDashboard,LogOut,Search,Settings,Shield,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Camera,LayoutDashboard,LogOut,Search,Settings,Shield,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Camera,LayoutDashboard,LogOut,Search,Settings,Shield,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Camera,LayoutDashboard,LogOut,Search,Settings,Shield,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Camera,LayoutDashboard,LogOut,Search,Settings,Shield,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Bell,Camera,LayoutDashboard,LogOut,Search,Settings,Shield,User,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        badge: null\n    },\n    {\n        name: \"Live Events\",\n        href: \"/events\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        badge: \"12\"\n    },\n    {\n        name: \"Cameras\",\n        href: \"/cameras\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        badge: null\n    },\n    {\n        name: \"Discovery\",\n        href: \"/discovery\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        badge: null\n    },\n    {\n        name: \"Analytics\",\n        href: \"/analytics\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        badge: null\n    },\n    {\n        name: \"AI Models\",\n        href: \"/ai-models\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        badge: \"New\"\n    },\n    {\n        name: \"Notifications\",\n        href: \"/notifications\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        badge: \"3\"\n    },\n    {\n        name: \"Users\",\n        href: \"/users\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        badge: null\n    },\n    {\n        name: \"Security\",\n        href: \"/security\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        badge: null\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        badge: null\n    }\n];\nfunction Sidebar({ className }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const handleLogout = async ()=>{\n        try {\n            await logout();\n            window.location.href = '/login';\n        } catch (error) {\n            console.error('Logout failed:', error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex h-full w-64 flex-col bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center px-6 border-b border-gray-200 dark:border-gray-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-lg font-bold text-gray-900 dark:text-white\",\n                                    children: \"HikVision AI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                    children: \"Monitoring System\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 space-y-1 px-3 py-4\",\n                children: navigation.map((item)=>{\n                    const isActive = pathname === item.href;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: item.href,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_18__.motion.div, {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"group flex items-center justify-between rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200\", isActive ? \"bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400\" : \"text-gray-700 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white\"),\n                            whileHover: {\n                                scale: 1.02\n                            },\n                            whileTap: {\n                                scale: 0.98\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"h-5 w-5 transition-colors\", isActive ? \"text-blue-600 dark:text-blue-400\" : \"text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 17\n                                }, this),\n                                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: isActive ? \"default\" : \"secondary\",\n                                    size: \"sm\",\n                                    className: \"ml-auto\",\n                                    children: item.badge\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 15\n                        }, this)\n                    }, item.name, false, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 dark:border-gray-800 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3 rounded-lg bg-green-50 dark:bg-green-900/20 p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex h-2 w-2 items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2 w-2 rounded-full bg-green-500 animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-green-800 dark:text-green-400\",\n                                        children: \"System Online\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-green-600 dark:text-green-500\",\n                                        children: \"All services running\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this),\n                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-200 dark:border-gray-700 p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-900 dark:text-white truncate\",\n                                                children: [\n                                                    user.firstName,\n                                                    \" \",\n                                                    user.lastName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                children: user.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: \"outline\",\n                                                className: \"text-xs mt-1\",\n                                                children: user.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                onClick: handleLogout,\n                                variant: \"outline\",\n                                size: \"sm\",\n                                className: \"w-full flex items-center justify-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Bell_Camera_LayoutDashboard_LogOut_Search_Settings_Shield_User_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\layout\\\\sidebar.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300\",\n            secondary: \"border-transparent bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300\",\n            destructive: \"border-transparent bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\",\n            success: \"border-transparent bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\",\n            warning: \"border-transparent bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300\",\n            outline: \"text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600\",\n            purple: \"border-transparent bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300\",\n            pink: \"border-transparent bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300\"\n        },\n        size: {\n            default: \"px-2.5 py-0.5 text-xs\",\n            sm: \"px-2 py-0.5 text-xs\",\n            lg: \"px-3 py-1 text-sm\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Badge({ className, variant, size, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant,\n            size\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95\", {\n    variants: {\n        variant: {\n            default: \"bg-blue-600 text-white shadow-lg hover:bg-blue-700 hover:shadow-xl\",\n            destructive: \"bg-red-600 text-white shadow-lg hover:bg-red-700 hover:shadow-xl\",\n            outline: \"border border-gray-300 bg-transparent hover:bg-gray-50 hover:border-gray-400 dark:border-gray-600 dark:hover:bg-gray-800 dark:hover:border-gray-500\",\n            secondary: \"bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700\",\n            ghost: \"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100\",\n            link: \"text-blue-600 underline-offset-4 hover:underline dark:text-blue-400\",\n            success: \"bg-green-600 text-white shadow-lg hover:bg-green-700 hover:shadow-xl\",\n            warning: \"bg-amber-600 text-white shadow-lg hover:bg-amber-700 hover:shadow-xl\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-12 rounded-lg px-8 text-base\",\n            xl: \"h-14 rounded-xl px-10 text-lg\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"mr-2 h-4 w-4 animate-spin\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 59,\n                columnNumber: 11\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 52,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-800 dark:bg-gray-900\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-gray-600 dark:text-gray-400\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9jYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDRTtBQUVoQyxNQUFNRSxxQkFBT0YsNkNBQWdCLENBRzNCLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUNDRCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FDWCwwSUFDQUc7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkgsS0FBS00sV0FBVyxHQUFHO0FBRW5CLE1BQU1DLDJCQUFhVCw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLGlDQUFpQ0c7UUFDOUMsR0FBR0MsS0FBSzs7Ozs7O0FBR2JJLFdBQVdELFdBQVcsR0FBRztBQUV6QixNQUFNRSwwQkFBWVYsNkNBQWdCLENBR2hDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDSztRQUNDTCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FDWCx1RkFDQUc7UUFFRCxHQUFHQyxLQUFLOzs7Ozs7QUFHYkssVUFBVUYsV0FBVyxHQUFHO0FBRXhCLE1BQU1JLGdDQUFrQlosNkNBQWdCLENBR3RDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDTztRQUNDUCxLQUFLQTtRQUNMRixXQUFXSCw4Q0FBRUEsQ0FBQyw0Q0FBNENHO1FBQ3pELEdBQUdDLEtBQUs7Ozs7OztBQUdiTyxnQkFBZ0JKLFdBQVcsR0FBRztBQUU5QixNQUFNTSw0QkFBY2QsNkNBQWdCLENBR2xDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDQztRQUFJRCxLQUFLQTtRQUFLRixXQUFXSCw4Q0FBRUEsQ0FBQyxZQUFZRztRQUFhLEdBQUdDLEtBQUs7Ozs7OztBQUVoRVMsWUFBWU4sV0FBVyxHQUFHO0FBRTFCLE1BQU1PLDJCQUFhZiw2Q0FBZ0IsQ0FHakMsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xGLFdBQVdILDhDQUFFQSxDQUFDLDhCQUE4Qkc7UUFDM0MsR0FBR0MsS0FBSzs7Ozs7O0FBR2JVLFdBQVdQLFdBQVcsR0FBRztBQUV1RCIsInNvdXJjZXMiOlsiQzpcXEFVR01FTlQgTkVXIFhNTCBISUtWSVNJT05cXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxjYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBDYXJkID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwicm91bmRlZC14bCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGJnLXdoaXRlIHNoYWRvdy1zbSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2hhZG93LW1kIGRhcms6Ym9yZGVyLWdyYXktODAwIGRhcms6YmctZ3JheS05MDBcIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmQuZGlzcGxheU5hbWUgPSBcIkNhcmRcIlxuXG5jb25zdCBDYXJkSGVhZGVyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcImZsZXggZmxleC1jb2wgc3BhY2UteS0xLjUgcC02XCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmRIZWFkZXIuZGlzcGxheU5hbWUgPSBcIkNhcmRIZWFkZXJcIlxuXG5jb25zdCBDYXJkVGl0bGUgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MUGFyYWdyYXBoRWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTEhlYWRpbmdFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8aDNcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIGxlYWRpbmctbm9uZSB0cmFja2luZy10aWdodCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC1ncmF5LTEwMFwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZFRpdGxlLmRpc3BsYXlOYW1lID0gXCJDYXJkVGl0bGVcIlxuXG5jb25zdCBDYXJkRGVzY3JpcHRpb24gPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBIVE1MUGFyYWdyYXBoRWxlbWVudCxcbiAgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTFBhcmFncmFwaEVsZW1lbnQ+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxwXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDBcIiwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuQ2FyZERlc2NyaXB0aW9uLmRpc3BsYXlOYW1lID0gXCJDYXJkRGVzY3JpcHRpb25cIlxuXG5jb25zdCBDYXJkQ29udGVudCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIEhUTUxEaXZFbGVtZW50LFxuICBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPGRpdiByZWY9e3JlZn0gY2xhc3NOYW1lPXtjbihcInAtNiBwdC0wXCIsIGNsYXNzTmFtZSl9IHsuLi5wcm9wc30gLz5cbikpXG5DYXJkQ29udGVudC5kaXNwbGF5TmFtZSA9IFwiQ2FyZENvbnRlbnRcIlxuXG5jb25zdCBDYXJkRm9vdGVyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgSFRNTERpdkVsZW1lbnQsXG4gIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcImZsZXggaXRlbXMtY2VudGVyIHAtNiBwdC0wXCIsIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkNhcmRGb290ZXIuZGlzcGxheU5hbWUgPSBcIkNhcmRGb290ZXJcIlxuXG5leHBvcnQgeyBDYXJkLCBDYXJkSGVhZGVyLCBDYXJkRm9vdGVyLCBDYXJkVGl0bGUsIENhcmREZXNjcmlwdGlvbiwgQ2FyZENvbnRlbnQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJDYXJkIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwiZGl2IiwiZGlzcGxheU5hbWUiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiaDMiLCJDYXJkRGVzY3JpcHRpb24iLCJwIiwiQ2FyZENvbnRlbnQiLCJDYXJkRm9vdGVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Check if user is authenticated on app load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuthStatus();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const checkAuthStatus = async ()=>{\n        try {\n            if (_lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.isAuthenticated()) {\n                const userProfile = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getProfile();\n                setUser(userProfile);\n            } else {\n                setUser(null);\n            }\n        } catch (error) {\n            console.error('Failed to check auth status:', error);\n            // Clear invalid tokens\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.logout();\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.login({\n                email,\n                password\n            });\n            setUser(response.user);\n        } catch (error) {\n            throw error;\n        }\n    };\n    const register = async (userData)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.register(userData);\n            setUser(response.user);\n        } catch (error) {\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.logout();\n            setUser(null);\n        } catch (error) {\n            console.error('Logout error:', error);\n            // Clear user state even if logout request fails\n            setUser(null);\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            if (_lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.isAuthenticated()) {\n                const userProfile = await _lib_api__WEBPACK_IMPORTED_MODULE_2__.apiService.getProfile();\n                setUser(userProfile);\n            }\n        } catch (error) {\n            console.error('Failed to refresh user:', error);\n            // If refresh fails, user might need to login again\n            setUser(null);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        isAuthenticated: !!user,\n        login,\n        register,\n        logout,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\AUGMENT NEW XML HIKVISION\\\\frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useSocket.ts":
/*!********************************!*\
  !*** ./src/hooks/useSocket.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCameraEvents: () => (/* binding */ useCameraEvents),\n/* harmony export */   useSocket: () => (/* binding */ useSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! socket.io-client */ \"(ssr)/./node_modules/socket.io-client/build/esm-debug/index.js\");\n/* __next_internal_client_entry_do_not_use__ useSocket,useCameraEvents auto */ \n\nfunction useSocket() {\n    const socketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [connectionError, setConnectionError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSocket.useEffect\": ()=>{\n            // Initialize socket connection\n            const socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_1__.io)(\"http://localhost:5000\" || 0, {\n                transports: [\n                    'websocket',\n                    'polling'\n                ],\n                timeout: 10000,\n                reconnection: true,\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000\n            });\n            socketRef.current = socket;\n            // Connection event handlers\n            socket.on('connect', {\n                \"useSocket.useEffect\": ()=>{\n                    console.log('Socket.IO connected:', socket.id);\n                    setIsConnected(true);\n                    setConnectionError(null);\n                }\n            }[\"useSocket.useEffect\"]);\n            socket.on('disconnect', {\n                \"useSocket.useEffect\": (reason)=>{\n                    console.log('Socket.IO disconnected:', reason);\n                    setIsConnected(false);\n                }\n            }[\"useSocket.useEffect\"]);\n            socket.on('connect_error', {\n                \"useSocket.useEffect\": (error)=>{\n                    console.error('Socket.IO connection error:', error);\n                    setConnectionError(error.message);\n                    setIsConnected(false);\n                }\n            }[\"useSocket.useEffect\"]);\n            socket.on('welcome', {\n                \"useSocket.useEffect\": (data)=>{\n                    console.log('Welcome message:', data);\n                }\n            }[\"useSocket.useEffect\"]);\n            // Cleanup on unmount\n            return ({\n                \"useSocket.useEffect\": ()=>{\n                    socket.disconnect();\n                }\n            })[\"useSocket.useEffect\"];\n        }\n    }[\"useSocket.useEffect\"], []);\n    // Subscribe to specific events\n    const subscribe = (event, callback)=>{\n        if (socketRef.current) {\n            socketRef.current.on(event, callback);\n        }\n        // Return unsubscribe function\n        return ()=>{\n            if (socketRef.current) {\n                socketRef.current.off(event, callback);\n            }\n        };\n    };\n    // Emit events\n    const emit = (event, data)=>{\n        if (socketRef.current && isConnected) {\n            socketRef.current.emit(event, data);\n        }\n    };\n    return {\n        socket: socketRef.current,\n        isConnected,\n        connectionError,\n        subscribe,\n        emit\n    };\n}\n// Hook for camera-specific events\nfunction useCameraEvents() {\n    const { subscribe, isConnected } = useSocket();\n    const [cameraEvents, setCameraEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [cameraStatuses, setCameraStatuses] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Map());\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useCameraEvents.useEffect\": ()=>{\n            // Subscribe to camera events\n            const unsubscribeEvent = subscribe('cameraEvent', {\n                \"useCameraEvents.useEffect.unsubscribeEvent\": (data)=>{\n                    console.log('Camera event received:', data);\n                    setCameraEvents({\n                        \"useCameraEvents.useEffect.unsubscribeEvent\": (prev)=>[\n                                data,\n                                ...prev.slice(0, 99)\n                            ]\n                    }[\"useCameraEvents.useEffect.unsubscribeEvent\"]) // Keep last 100 events\n                    ;\n                }\n            }[\"useCameraEvents.useEffect.unsubscribeEvent\"]);\n            const unsubscribeStatus = subscribe('cameraStatusUpdate', {\n                \"useCameraEvents.useEffect.unsubscribeStatus\": (data)=>{\n                    console.log('Camera status update:', data);\n                    setCameraStatuses({\n                        \"useCameraEvents.useEffect.unsubscribeStatus\": (prev)=>new Map(prev.set(data.cameraId, data.status))\n                    }[\"useCameraEvents.useEffect.unsubscribeStatus\"]);\n                }\n            }[\"useCameraEvents.useEffect.unsubscribeStatus\"]);\n            const unsubscribeArmed = subscribe('cameraArmed', {\n                \"useCameraEvents.useEffect.unsubscribeArmed\": (data)=>{\n                    console.log('Camera armed:', data);\n                // You can add additional logic here for armed state updates\n                }\n            }[\"useCameraEvents.useEffect.unsubscribeArmed\"]);\n            const unsubscribeDisarmed = subscribe('cameraDisarmed', {\n                \"useCameraEvents.useEffect.unsubscribeDisarmed\": (data)=>{\n                    console.log('Camera disarmed:', data);\n                // You can add additional logic here for disarmed state updates\n                }\n            }[\"useCameraEvents.useEffect.unsubscribeDisarmed\"]);\n            return ({\n                \"useCameraEvents.useEffect\": ()=>{\n                    unsubscribeEvent();\n                    unsubscribeStatus();\n                    unsubscribeArmed();\n                    unsubscribeDisarmed();\n                }\n            })[\"useCameraEvents.useEffect\"];\n        }\n    }[\"useCameraEvents.useEffect\"], [\n        subscribe\n    ]);\n    return {\n        isConnected,\n        cameraEvents,\n        cameraStatuses,\n        clearEvents: ()=>setCameraEvents([])\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useSocket.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiService: () => (/* binding */ apiService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// API Service Layer for Frontend-Backend Integration\nclass ApiService {\n    constructor(){\n        this.accessToken = null;\n        this.refreshToken = null;\n        this.baseUrl = \"http://localhost:5000\" || 0;\n        // Load tokens from localStorage on initialization\n        if (false) {}\n    }\n    // ===== PRIVATE HELPER METHODS =====\n    async makeRequest(endpoint, options = {}) {\n        const url = `${this.baseUrl}${endpoint}`;\n        const config = {\n            headers: {\n                'Content-Type': 'application/json',\n                ...options.headers\n            },\n            ...options\n        };\n        // Add authorization header if token exists\n        if (this.accessToken) {\n            config.headers = {\n                ...config.headers,\n                Authorization: `Bearer ${this.accessToken}`\n            };\n        }\n        try {\n            const response = await fetch(url, config);\n            // Handle 401 - try to refresh token\n            if (response.status === 401 && this.refreshToken) {\n                const refreshed = await this.refreshAccessToken();\n                if (refreshed) {\n                    // Retry the original request with new token\n                    config.headers = {\n                        ...config.headers,\n                        Authorization: `Bearer ${this.accessToken}`\n                    };\n                    const retryResponse = await fetch(url, config);\n                    return this.handleResponse(retryResponse);\n                }\n            }\n            return this.handleResponse(response);\n        } catch (error) {\n            console.error('API request failed:', error);\n            throw new Error('Network error occurred');\n        }\n    }\n    async handleResponse(response) {\n        const contentType = response.headers.get('content-type');\n        const isJson = contentType?.includes('application/json');\n        const data = isJson ? await response.json() : await response.text();\n        if (!response.ok) {\n            const error = {\n                error: data.error || `HTTP ${response.status}`,\n                details: data.details,\n                status: response.status\n            };\n            throw error;\n        }\n        return data;\n    }\n    setTokens(accessToken, refreshToken) {\n        this.accessToken = accessToken;\n        this.refreshToken = refreshToken;\n        if (false) {}\n    }\n    clearTokens() {\n        this.accessToken = null;\n        this.refreshToken = null;\n        if (false) {}\n    }\n    // ===== AUTHENTICATION METHODS =====\n    async login(credentials) {\n        const response = await this.makeRequest('/api/auth/login', {\n            method: 'POST',\n            body: JSON.stringify(credentials)\n        });\n        // Backend returns token and refreshToken directly, not nested under tokens\n        this.setTokens(response.token, response.refreshToken);\n        // Transform response to match expected AuthResponse format\n        return {\n            message: response.message,\n            user: response.user,\n            tokens: {\n                accessToken: response.token,\n                refreshToken: response.refreshToken\n            }\n        };\n    }\n    async register(userData) {\n        const response = await this.makeRequest('/api/auth/register', {\n            method: 'POST',\n            body: JSON.stringify(userData)\n        });\n        // Backend returns token and refreshToken directly, not nested under tokens\n        this.setTokens(response.token, response.refreshToken);\n        // Transform response to match expected AuthResponse format\n        return {\n            message: response.message,\n            user: response.user,\n            tokens: {\n                accessToken: response.token,\n                refreshToken: response.refreshToken\n            }\n        };\n    }\n    async refreshAccessToken() {\n        if (!this.refreshToken) return false;\n        try {\n            const response = await this.makeRequest('/api/auth/refresh', {\n                method: 'POST',\n                body: JSON.stringify({\n                    refreshToken: this.refreshToken\n                })\n            });\n            // Backend returns token and refreshToken directly, not nested under tokens\n            this.setTokens(response.token, response.refreshToken);\n            return true;\n        } catch (error) {\n            this.clearTokens();\n            return false;\n        }\n    }\n    async logout() {\n        this.clearTokens();\n    }\n    async getProfile() {\n        const response = await this.makeRequest('/api/auth/me');\n        return response.user;\n    }\n    // ===== CAMERA METHODS =====\n    async getCameras(filters) {\n        const params = new URLSearchParams();\n        if (filters?.status) params.append('status', filters.status);\n        if (filters?.location) params.append('location', filters.location);\n        if (filters?.limit) params.append('limit', filters.limit.toString());\n        if (filters?.page) params.append('page', filters.page.toString());\n        const queryString = params.toString();\n        const endpoint = `/api/cameras${queryString ? `?${queryString}` : ''}`;\n        return this.makeRequest(endpoint);\n    }\n    async getCamera(id) {\n        const response = await this.makeRequest(`/api/cameras/${id}`);\n        return response.camera;\n    }\n    async createCamera(cameraData) {\n        const response = await this.makeRequest('/api/cameras', {\n            method: 'POST',\n            body: JSON.stringify(cameraData)\n        });\n        return response.camera;\n    }\n    async updateCamera(id, updates) {\n        const response = await this.makeRequest(`/api/cameras/${id}`, {\n            method: 'PATCH',\n            body: JSON.stringify(updates)\n        });\n        return response.camera;\n    }\n    async deleteCamera(id) {\n        await this.makeRequest(`/api/cameras/${id}`, {\n            method: 'DELETE'\n        });\n    }\n    async armCamera(id) {\n        await this.makeRequest(`/api/cameras/${id}/arm`, {\n            method: 'POST'\n        });\n    }\n    async disarmCamera(id) {\n        await this.makeRequest(`/api/cameras/${id}/disarm`, {\n            method: 'POST'\n        });\n    }\n    async getCameraStatus(id) {\n        return this.makeRequest(`/api/cameras/${id}/status`);\n    }\n    async getCameraSnapshot(id) {\n        const response = await fetch(`${this.baseURL}/api/cameras/${id}/snapshot`, {\n            headers: {\n                'Authorization': `Bearer ${this.accessToken}`\n            }\n        });\n        if (!response.ok) {\n            throw new Error('Failed to get camera snapshot');\n        }\n        return response.blob();\n    }\n    async getCameraStreamUrl(id) {\n        return this.makeRequest(`/api/cameras/${id}/stream`);\n    }\n    // Discovery API methods\n    async startNetworkScan(scanOptions) {\n        return this.makeRequest('/api/discovery/scan', {\n            method: 'POST',\n            body: JSON.stringify(scanOptions)\n        });\n    }\n    async getScanStatus() {\n        return this.makeRequest('/api/discovery/status');\n    }\n    async stopNetworkScan() {\n        return this.makeRequest('/api/discovery/stop', {\n            method: 'POST'\n        });\n    }\n    async pingHost(ipAddress, port, timeout) {\n        return this.makeRequest('/api/discovery/ping', {\n            method: 'POST',\n            body: JSON.stringify({\n                ipAddress,\n                port,\n                timeout\n            })\n        });\n    }\n    async testCameraConnection(credentials) {\n        return this.makeRequest('/api/discovery/test-camera', {\n            method: 'POST',\n            body: JSON.stringify(credentials)\n        });\n    }\n    async getCameraStats() {\n        return this.makeRequest('/api/cameras/stats');\n    }\n    // ===== EVENT METHODS =====\n    async getEvents(filters) {\n        const params = new URLSearchParams();\n        if (filters?.type) params.append('type', filters.type);\n        if (filters?.severity) params.append('severity', filters.severity);\n        if (filters?.status) params.append('status', filters.status);\n        if (filters?.cameraId) params.append('cameraId', filters.cameraId);\n        if (filters?.startDate) params.append('startDate', filters.startDate);\n        if (filters?.endDate) params.append('endDate', filters.endDate);\n        if (filters?.limit) params.append('limit', filters.limit.toString());\n        if (filters?.page) params.append('page', filters.page.toString());\n        const queryString = params.toString();\n        const endpoint = `/api/events${queryString ? `?${queryString}` : ''}`;\n        return this.makeRequest(endpoint);\n    }\n    async getEvent(id) {\n        const response = await this.makeRequest(`/api/events/${id}`);\n        return response.event;\n    }\n    async createEvent(eventData) {\n        const response = await this.makeRequest('/api/events', {\n            method: 'POST',\n            body: JSON.stringify(eventData)\n        });\n        return response.event;\n    }\n    async acknowledgeEvent(id, data) {\n        const response = await this.makeRequest(`/api/events/${id}/acknowledge`, {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n        return response.event;\n    }\n    async resolveEvent(id, data) {\n        const response = await this.makeRequest(`/api/events/${id}/resolve`, {\n            method: 'POST',\n            body: JSON.stringify(data)\n        });\n        return response.event;\n    }\n    async getEventStats() {\n        return this.makeRequest('/api/events/stats');\n    }\n    // ===== SYSTEM METHODS =====\n    async getHealth() {\n        return this.makeRequest('/health');\n    }\n    async getSystemStatus() {\n        return this.makeRequest('/api/status');\n    }\n    // ===== UTILITY METHODS =====\n    isAuthenticated() {\n        return !!this.accessToken;\n    }\n    getAccessToken() {\n        return this.accessToken;\n    }\n}\n// Export singleton instance\nconst apiService = new ApiService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (apiService);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   throttle: () => (/* binding */ throttle)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(new Date(date));\n}\nfunction formatFileSize(bytes) {\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\",\n        \"TB\"\n    ];\n    if (bytes === 0) return \"0 Bytes\";\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + \" \" + sizes[i];\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/ws","vendor-chunks/tailwind-merge","vendor-chunks/engine.io-client","vendor-chunks/socket.io-client","vendor-chunks/socket.io-parser","vendor-chunks/lucide-react","vendor-chunks/xmlhttprequest-ssl","vendor-chunks/motion-utils","vendor-chunks/engine.io-parser","vendor-chunks/@socket.io","vendor-chunks/class-variance-authority","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/has-flag"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CAUGMENT%20NEW%20XML%20HIKVISION%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CAUGMENT%20NEW%20XML%20HIKVISION%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();